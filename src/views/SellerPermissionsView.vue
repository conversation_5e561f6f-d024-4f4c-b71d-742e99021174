<script setup>
import { ref, onMounted, computed } from 'vue'
import { useToast } from '@/services/toast'
import DataTable from '@/components/DataTable.vue'
import Pagination from '@/components/Pagination.vue'
import usePagination from '@/composables/usePagination'
import PageHeader from '@/components/ui/PageHeader.vue'
import Container from '@/components/ui/Container.vue'
import { sellerPermissionsApi } from '@/services/api'
import { copyToClipboard, formatDate } from '@/utils/tableUtils'

const { error: showError, success: showSuccess } = useToast()

// Modal states
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const permissionToDelete = ref(null)
const permissionToEdit = ref(null)
const isCreating = ref(false)
const isUpdating = ref(false)
const isDeleting = ref(false)

// Form data
const formData = ref({
  sellerId: '',
  userId: '',
  userDocumentId: '',
  roles: []
})

const editFormData = ref({
  roles: []
})

// Computed properties
const isFormValid = computed(() => {
  return (
    formData.value.sellerId &&
    formData.value.roles.length > 0 &&
    (formData.value.userId || formData.value.userDocumentId)
  )
})

// Fetch function for pagination
const fetchSellerPermissionsWithFilters = async (params) => {
  try {
    const [response] = await Promise.all([
      sellerPermissionsApi.getSellerPermissions(params),
      new Promise((resolve) => setTimeout(resolve, 500))
    ])
    return response
  } catch (error) {
    console.error('Failed to fetch seller permissions:', error)
    showError(error.data?.message || 'Failed to fetch seller permissions')
    throw error
  }
}

// Setup pagination
const pagination = usePagination(fetchSellerPermissionsWithFilters, {
  defaultPageSize: 10
})

// Methods
const handleCopyToClipboard = (text) => {
  copyToClipboard(text, showSuccess, showError)
}

const editPermission = (permission) => {
  permissionToEdit.value = permission
  editFormData.value.roles = [...permission.roles]
  showEditModal.value = true
}

const deletePermission = (permission) => {
  permissionToDelete.value = permission
  showDeleteModal.value = true
}

const createPermission = async () => {
  isCreating.value = true
  try {
    await sellerPermissionsApi.createSellerPermission(formData.value)
    showSuccess('Seller permission created successfully')
    closeCreateModal()
    pagination.fetchPage()
  } catch (error) {
    console.error('Failed to create seller permission:', error)
    showError(error.data?.message || 'Failed to create seller permission')
  } finally {
    isCreating.value = false
  }
}

const updatePermission = async () => {
  isUpdating.value = true
  try {
    await sellerPermissionsApi.updateSellerPermission(permissionToEdit.value.id, editFormData.value)
    showSuccess('Seller permission updated successfully')
    closeEditModal()
    pagination.fetchPage()
  } catch (error) {
    console.error('Failed to update seller permission:', error)
    showError(error.data?.message || 'Failed to update seller permission')
  } finally {
    isUpdating.value = false
  }
}

const confirmDelete = async () => {
  isDeleting.value = true
  try {
    await sellerPermissionsApi.deleteSellerPermission(permissionToDelete.value.id)
    showSuccess('Seller permission deleted successfully')
    showDeleteModal.value = false
    permissionToDelete.value = null
    pagination.fetchPage()
  } catch (error) {
    console.error('Failed to delete seller permission:', error)
    showError(error.data?.message || 'Failed to delete seller permission')
  } finally {
    isDeleting.value = false
  }
}

const closeCreateModal = () => {
  showCreateModal.value = false
  formData.value = {
    sellerId: '',
    userId: '',
    userDocumentId: '',
    roles: []
  }
}

const closeEditModal = () => {
  showEditModal.value = false
  permissionToEdit.value = null
  editFormData.value = {
    roles: []
  }
}

// Initialize
onMounted(() => {
  pagination.fetchPage()
})
</script>

<template>
  <Container>
    <PageHeader title="Seller Permissions" subtitle="Manage seller-level permissions and roles" />

    <!-- Actions Section -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex gap-2">
        <button class="btn btn-primary" @click="showCreateModal = true">
          <vue-feather type="plus" size="16" class="mr-2" />
          Create Permission
        </button>
      </div>
    </div>

    <!-- Data Table -->
    <DataTable
      ref="dataTable"
      :items="pagination.displayedItems.value"
      :loading="pagination.isLoading.value"
      :use-skeleton-loader="true"
      :skeleton-row-height="'64px'"
      :animate-rows="true"
      :columns="[
        {
          key: 'id',
          label: 'ID',
          skeletonWidth: '120px'
        },
        {
          key: 'sellerId',
          label: 'Seller ID',
          skeletonWidth: '150px'
        },
        {
          key: 'userId',
          label: 'User ID',
          skeletonWidth: '150px'
        },
        {
          key: 'userDocumentId',
          label: 'User Document ID',
          skeletonWidth: '150px'
        },
        {
          key: 'roles',
          label: 'Roles',
          skeletonWidth: '120px'
        },
        {
          key: 'insertedAt',
          label: 'Created At',
          skeletonWidth: '80px',
          showSecondaryLine: true,
          secondaryLineWidth: '80px'
        }
      ]"
      empty-text="No seller permissions found"
    >
      <!-- ID column -->
      <template #cell-id="{ item }">
        <div class="flex items-center gap-2 h-full">
          <div class="font-mono text-xs bg-base-200 p-1.5 rounded-md">
            {{ item.id.substring(0, 8) }}...
          </div>
          <button
            @click.stop="handleCopyToClipboard(item.id)"
            class="btn btn-xs btn-ghost p-1"
            title="Copy ID"
          >
            <feather-icon type="copy" size="16" class="h-4 w-4 text-primary" />
          </button>
        </div>
      </template>

      <!-- Seller ID column -->
      <template #cell-sellerId="{ item }">
        <div class="flex items-center gap-2 h-full">
          <div class="font-mono text-xs bg-base-200 p-1.5 rounded-md">
            {{ item.sellerId ? item.sellerId.substring(0, 8) + '...' : '' }}
          </div>
          <button
            v-if="item.sellerId"
            @click.stop="handleCopyToClipboard(item.sellerId)"
            class="btn btn-xs btn-ghost p-1"
            title="Copy Seller ID"
          >
            <feather-icon type="copy" size="16" class="h-4 w-4 text-primary" />
          </button>
        </div>
      </template>

      <!-- User ID column -->
      <template #cell-userId="{ item }">
        <div class="flex items-center gap-2 h-full">
          <div v-if="item.userId" class="font-mono text-xs bg-base-200 p-1.5 rounded-md">
            {{ item.userId.substring(0, 8) }}...
          </div>
          <span v-else class="text-base-content/50 italic">N/A</span>
          <button
            v-if="item.userId"
            @click.stop="handleCopyToClipboard(item.userId)"
            class="btn btn-xs btn-ghost p-1"
            title="Copy User ID"
          >
            <feather-icon type="copy" size="16" class="h-4 w-4 text-primary" />
          </button>
        </div>
      </template>

      <!-- User Document ID column -->
      <template #cell-userDocumentId="{ item }">
        <div class="flex items-center gap-2 h-full">
          <div v-if="item.userDocumentId" class="font-mono text-xs bg-base-200 p-1.5 rounded-md">
            {{
              item.userDocumentId.length > 12
                ? item.userDocumentId.substring(0, 12) + '...'
                : item.userDocumentId
            }}
          </div>
          <span v-else class="text-base-content/50 italic">N/A</span>
          <button
            v-if="item.userDocumentId"
            @click.stop="handleCopyToClipboard(item.userDocumentId)"
            class="btn btn-xs btn-ghost p-1"
            title="Copy User Document ID"
          >
            <feather-icon type="copy" size="16" class="h-4 w-4 text-primary" />
          </button>
        </div>
      </template>

      <!-- Roles column -->
      <template #cell-roles="{ item }">
        <div class="flex items-center gap-1 h-full">
          <span
            v-for="role in item.roles"
            :key="role"
            class="badge badge-sm"
            :class="{
              'badge-primary': role === 'ADMIN',
              'badge-secondary': role === 'OWNER'
            }"
          >
            {{ role }}
          </span>
        </div>
      </template>

      <!-- Created At column -->
      <template #cell-insertedAt="{ item }">
        <div class="flex flex-col justify-center h-full">
          <span class="font-medium">{{ formatDate(item.insertedAt).split(',')[0] }}</span>
          <span class="text-xs text-base-content/70">{{
            formatDate(item.insertedAt).split(',')[1]
          }}</span>
        </div>
      </template>

      <!-- Actions column -->
      <template #actions="{ item }">
        <div class="flex justify-end items-center h-full gap-1">
          <button
            @click.stop="editPermission(item)"
            class="btn btn-sm btn-ghost text-primary hover:bg-primary hover:bg-opacity-20 group"
            title="Edit permission"
          >
            <feather-icon type="edit-2" size="16" class="h-4 w-4 group-hover:stroke-black" />
          </button>
          <button
            @click.stop="deletePermission(item)"
            class="btn btn-sm btn-ghost text-error hover:bg-error hover:bg-opacity-20 group"
            title="Delete permission"
          >
            <feather-icon type="trash-2" size="16" class="h-4 w-4 group-hover:stroke-black" />
          </button>
        </div>
      </template>
    </DataTable>

    <!-- Pagination -->
    <Pagination
      v-if="pagination.hasPagination.value"
      :current-page="pagination.currentPage.value"
      :total-pages="pagination.totalPages.value"
      @page-change="pagination.goToPage"
    />

    <!-- Create Permission Modal -->
    <dialog :class="['modal', { 'modal-open': showCreateModal }]">
      <div class="modal-box max-w-2xl">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-2xl font-bold text-primary">Create Seller Permission</h3>
          <button type="button" class="btn btn-sm btn-circle btn-ghost" @click="closeCreateModal">
            <vue-feather type="x" size="18" />
          </button>
        </div>

        <form @submit.prevent="createPermission" class="space-y-6">
          <!-- Seller ID -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-semibold">Seller ID *</span>
            </label>
            <input
              v-model="formData.sellerId"
              type="text"
              placeholder="Enter seller UUID"
              class="input input-bordered w-full focus:input-primary"
              required
            />
            <label class="label">
              <span class="label-text-alt text-gray-500"
                >The UUID of the seller to grant permissions for</span
              >
            </label>
          </div>

          <!-- User Identification -->
          <div class="divider">User Identification</div>
          <div class="bg-base-200 p-4 rounded-lg">
            <p class="text-sm text-gray-600 mb-4">
              Provide either a User ID or User Document ID to identify the user.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text font-semibold">User ID</span>
                </label>
                <input
                  v-model="formData.userId"
                  type="text"
                  placeholder="Enter user UUID"
                  class="input input-bordered focus:input-primary"
                  :disabled="!!formData.userDocumentId"
                />
                <label class="label">
                  <span class="label-text-alt">Internal user ID</span>
                </label>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text font-semibold">User Document ID</span>
                </label>
                <input
                  v-model="formData.userDocumentId"
                  type="text"
                  placeholder="Enter document ID"
                  class="input input-bordered focus:input-primary"
                  :disabled="!!formData.userId"
                />
                <label class="label">
                  <span class="label-text-alt">External document identifier</span>
                </label>
              </div>
            </div>
          </div>

          <!-- Roles -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-semibold">Roles *</span>
            </label>
            <div class="bg-base-200 p-4 rounded-lg">
              <div class="space-y-3">
                <label class="flex items-center space-x-3 cursor-pointer">
                  <input
                    v-model="formData.roles"
                    type="checkbox"
                    value="ADMIN"
                    class="checkbox checkbox-primary"
                  />
                  <div>
                    <div class="font-medium">Admin</div>
                    <div class="text-sm text-gray-500">
                      Can manage seller settings and permissions
                    </div>
                  </div>
                </label>
                <label class="flex items-center space-x-3 cursor-pointer">
                  <input
                    v-model="formData.roles"
                    type="checkbox"
                    value="OWNER"
                    class="checkbox checkbox-primary"
                  />
                  <div>
                    <div class="font-medium">Owner</div>
                    <div class="text-sm text-gray-500">Full control over seller account</div>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <div class="modal-action pt-6">
            <button type="button" class="btn btn-ghost" @click="closeCreateModal">Cancel</button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="!isFormValid"
              :class="{ loading: isCreating }"
            >
              <vue-feather v-if="!isCreating" type="plus" size="16" class="mr-2" />
              {{ isCreating ? 'Creating...' : 'Create Permission' }}
            </button>
          </div>
        </form>
      </div>
    </dialog>

    <!-- Edit Permission Modal -->
    <dialog :class="['modal', { 'modal-open': showEditModal }]">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Edit Seller Permission</h3>

        <form @submit.prevent="updatePermission">
          <div class="form-control mb-4">
            <label class="label">
              <span class="label-text">Roles *</span>
            </label>
            <div class="flex gap-4">
              <label class="label cursor-pointer">
                <input
                  v-model="editFormData.roles"
                  type="checkbox"
                  value="ADMIN"
                  class="checkbox"
                />
                <span class="label-text ml-2">Admin</span>
              </label>
              <label class="label cursor-pointer">
                <input
                  v-model="editFormData.roles"
                  type="checkbox"
                  value="OWNER"
                  class="checkbox"
                />
                <span class="label-text ml-2">Owner</span>
              </label>
            </div>
          </div>

          <div class="modal-action">
            <button type="button" class="btn" @click="closeEditModal" :disabled="isUpdating">
              Cancel
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="editFormData.roles.length === 0 || isUpdating"
              :class="{ loading: isUpdating }"
            >
              {{ isUpdating ? 'Updating...' : 'Update' }}
            </button>
          </div>
        </form>
      </div>
    </dialog>

    <!-- Delete Confirmation Modal -->
    <dialog :class="['modal', { 'modal-open': showDeleteModal }]">
      <div class="modal-box">
        <h3 class="font-bold text-lg">Confirm Delete</h3>
        <p class="py-4">
          Are you sure you want to delete this seller permission? This action cannot be undone.
        </p>
        <div class="modal-action">
          <button class="btn" @click="showDeleteModal = false" :disabled="isDeleting">
            Cancel
          </button>
          <button
            class="btn btn-error"
            @click="confirmDelete"
            :disabled="isDeleting"
            :class="{ loading: isDeleting }"
          >
            {{ isDeleting ? 'Deleting...' : 'Delete' }}
          </button>
        </div>
      </div>
    </dialog>
  </Container>
</template>
