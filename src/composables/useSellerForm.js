import { ref, computed, reactive } from 'vue'
import sellersApi from '@/services/api/sellers'
import { useToast } from '@/services/toast'
import { useFormValidation } from '@/composables/useFormValidation'

/**
 * Seller form management composable
 * Handles seller creation form state, validation, and submission
 * @returns {Object} Form state and methods
 */
export function useSellerForm() {
  const { success: showSuccess, error: showError } = useToast()
  const validation = useFormValidation()
  const {
    fieldErrors,
    validateRequired,
    validateForm
  } = validation

  // Seller form data
  const sellerData = reactive({
    type: 'ORGANIZER',
    promoterId: '',
    displayName: ''
  })

  // Form state
  const loading = ref(false)
  const isSuccess = ref(false)
  const submitError = ref(null)

  // Seller types (currently only ORGANIZER is supported)
  const sellerTypes = [
    { id: 'ORGANIZER', label: 'Organizer' }
  ]

  // Computed properties
  const isFormValid = computed(() => {
    return sellerData.promoterId.trim() !== ''
  })

  /**
   * Validate UUID format
   * @param {string} value - UUID to validate
   * @param {string} [message] - Custom error message
   * @returns {string} Error message or empty string
   */
  function validateUUID(value, message) {
    if (!value) return ''
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(value) ? '' : message || 'Please enter a valid UUID'
  }

  /**
   * Perform form validation
   * @returns {boolean} Whether the form is valid
   */
  function performValidation() {
    // Reset validation state
    validation.resetValidation()

    // Create validation rules
    const validationRules = {
      promoterId: (value) => {
        const requiredError = validateRequired(value, 'Promoter ID is required')
        if (requiredError) return requiredError
        return validateUUID(value, 'Please enter a valid promoter UUID')
      }
    }

    // Add display name validation if provided
    if (sellerData.displayName.trim()) {
      validationRules.displayName = (value) => {
        if (value.length > 255) {
          return 'Display name must be less than 255 characters'
        }
        return ''
      }
    }

    // Run validation
    const isValid = validateForm(sellerData, validationRules)

    if (!isValid) {
      showError('Please correct the errors in the form')
    }

    return isValid
  }

  /**
   * Handle form submission
   * @returns {Promise<void>}
   */
  async function handleSubmit() {
    submitError.value = null
    isSuccess.value = false

    if (!performValidation()) {
      return
    }

    loading.value = true
    try {
      const payload = {
        type: sellerData.type,
        promoterId: sellerData.promoterId.trim()
      }

      // Only include displayName if it's provided
      if (sellerData.displayName.trim()) {
        payload.displayName = sellerData.displayName.trim()
      }

      const result = await sellersApi.createSeller(payload)
      showSuccess('Seller created successfully')
      isSuccess.value = true

      // Reset form
      resetForm()

      return result
    } catch (error) {
      console.error('Seller creation error:', error)

      // Handle API validation errors
      if (error?.data?.data) {
        for (const err of error.data.data) {
          fieldErrors.value[err.field] = err.message
        }
      }

      submitError.value = error?.data?.message || 'Failed to create seller'
      showError(submitError.value)
    } finally {
      loading.value = false
    }
  }

  /**
   * Reset the form to its initial state
   */
  function resetForm() {
    // Reset form data
    sellerData.type = 'ORGANIZER'
    sellerData.promoterId = ''
    sellerData.displayName = ''

    // Reset validation state
    validation.resetValidation()

    // Reset form state
    submitError.value = null
    isSuccess.value = false
  }

  return {
    // Form data
    sellerData,

    // Form state
    loading,
    isSuccess,
    fieldErrors,
    submitError,
    sellerTypes,

    // Computed
    isFormValid,

    // Methods
    handleSubmit,
    resetForm,
    performValidation
  }
}
