defmodule OrdersServiceWeb.Schemas.OrderDetailsResponse do
  @moduledoc false
  alias OpenApiSpex.Schema

  require OpenApiSpex

  defmodule BillResponse do
    @moduledoc false
    alias OpenApiSpex.Schema

    require OpenApiSpex

    OpenApiSpex.schema(%{
      title: "BillResponse",
      description: "Bill details",
      type: :object,
      # styler:sort
      properties: %{
        fee: %Schema{type: :integer, description: "Fee amount"},
        id: %Schema{description: "Bill ID", type: :string},
        presaleFee: %Schema{type: :integer, description: "Presale fee amount"},
        presaleFeeTax: %Schema{type: :integer, description: "Presale fee tax amount"},
        systemFee: %Schema{type: :integer, description: "System fee amount"},
        systemFeeTax: %Schema{type: :integer, description: "System fee tax amount"},
        total: %Schema{type: :integer, description: "Total amount"}
      }
    })
  end

  OpenApiSpex.schema(%{
    title: "OrderDetailsResponse",
    description: "Order details",
    type: :object,
    # styler:sort
    properties: %{
      bill: BillResponse,
      billId: %Schema{type: :string, description: "Bill ID"},
      billingAddress: OrdersServiceWeb.Schemas.BillingAddressResponse,
      createdByPersonalInformation: OrdersServiceWeb.Schemas.PersonalInformationResponse,
      deliveryAddress: OrdersServiceWeb.Schemas.OrderListResponse.AddressResponse,
      email: %Schema{type: :string, description: "Email of the order"},
      id: %Schema{type: :string, description: "Order ID"},
      payinTransactions: %Schema{
        type: :array,
        description: "List of payin transactions",
        items: OrdersServiceWeb.Schemas.PayinTransactionResponse
      },
      status: %Schema{type: :string, description: "Order status"},
      submittedDate: %Schema{type: :string, format: :datetime, description: "UTC Time when the order was submitted"}
    },
    # styler:sort
    example: %{
      bill: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
        fee: 500,
        presaleFee: 200,
        presaleFeeTax: 40,
        systemFee: 300,
        systemFeeTax: 60,
        total: 1100
      },
      billId: "ebc5feb5-07cc-4d52-9192-457a3dc64e5b",
      billingAddress: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5e",
        streetAddress: "123 Main St",
        locality: "Anytown",
        region: "CA",
        postalCode: "12345",
        countryIso: "US"
      },
      createdByPersonalInformation: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e59",
        givenName: "John",
        familyName: "Doe"
      },
      deliveryAddress: %{
        id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5f",
        streetAddress: "456 Oak St",
        locality: "Othertown",
        region: "NY",
        postalCode: "67890",
        countryIso: "US"
      },
      email: "<EMAIL>",
      id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5c",
      payinTransactions: [
        %{
          id: "ebc5feb5-07cc-4d52-9192-457a3dc64e5g",
          status: "completed",
          paymentMethod: "credit_card"
        }
      ],
      status: "PAID",
      submittedDate: "2022-01-01T00:00:00Z"
    }
  })
end
