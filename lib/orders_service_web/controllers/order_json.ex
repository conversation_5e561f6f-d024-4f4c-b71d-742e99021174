defmodule OrdersServiceWeb.OrderJSON do
  @moduledoc false

  alias ExServiceClient.Services.EventsService.Address
  alias ExServiceClient.Services.EventsService.Event
  alias ExServiceClient.Services.EventsService.Venue
  alias OrdersService.Bill
  alias OrdersServiceWeb.AddressJSON
  alias OrdersServiceWeb.PayinTransactionJSON
  alias OrdersServiceWeb.PersonalInformationJSON
  alias OrdersServiceWeb.TicketJSON

  def index(%{page: page}) do
    %{
      data: Enum.map(page.entries, &order_json/1),
      pageNumber: page.page_number,
      pageSize: page.page_size,
      totalPages: page.total_pages,
      totalEntries: page.total_entries
    }
  end

  def show(%{order: order}) do
    %{data: order}
  end

  def show_details(%{order: order}) do
    # styler:sort
    %{
      bill: bill_data(order.bill),
      billId: order.bill_id,
      billingAddress: order.billing_address && AddressJSON.data(order.billing_address),
      createdByPersonalInformation:
        order.created_by_personal_information &&
          PersonalInformationJSON.data(order.created_by_personal_information),
      deliveryAddress: order.delivery_address && AddressJSON.data(order.delivery_address),
      email: order.email,
      id: order.id,
      payinTransactions:
        order.payin_transactions &&
          Enum.map(order.payin_transactions, &PayinTransactionJSON.data/1),
      receiptNumber: order.receipt_number,
      status: order.status,
      submittedDate: order.submitted_date
    }
  end

  def internal_data(order) do
    created_by = order.created_by_personal_information

    # styler:sort
    %{
      billing_address: order.billing_address && AddressJSON.internal_data(order.billing_address),
      created_by_personal_information:
        created_by &&
          PersonalInformationJSON.internal_data(created_by),
      delivery_address: order.delivery_address && AddressJSON.internal_data(order.delivery_address),
      email: order.email,
      id: order.id,
      paid_date: order.paid_date,
      payer_email: order.payer_email,
      receipt_number: order.receipt_number,
      seller_id: order.seller_id,
      status: order.status,
      tickets: order.order_tickets && TicketJSON.internal_index(%{order_tickets: order.order_tickets})
    }
  end

  def internal_pubsub_update_data(order) do
    created_by = order.created_by_personal_information
    # hard-coded until the API needs to support boca prints
    print_type = "a4"

    %{
      id: order.id,
      email: order.email,
      paid_date: order.paid_date,
      payer_email: order.payer_email,
      status: order.status,
      tickets:
        order.order_tickets &&
          TicketJSON.internal_pubsub_update_data_index(%{order_tickets: order.order_tickets}),
      created_by_personal_information:
        created_by &&
          PersonalInformationJSON.internal_data(created_by),
      billing_address: order.billing_address && AddressJSON.internal_data(order.billing_address),
      delivery_address: order.delivery_address && AddressJSON.internal_data(order.delivery_address),
      seller_id: order.seller_id,
      print_type: print_type
    }
  end

  def order_summary_data(order, events, seller) do
    %{
      deliveryAddress: AddressJSON.order_summary_data(seller),
      orderNumber: order.id,
      positions: TicketJSON.order_summary_positions(%{order_tickets: order.order_tickets, events: events}),
      purchaseDate: order.paid_date,
      receiptNumber: order.receipt_number
    }
  end

  defp order_json({order, event}) do
    # styler:sort
    %{
      billId: order.bill_id,
      createdByPersonalInformation: PersonalInformationJSON.data(order.created_by_personal_information),
      email: order.email,
      event: event_json(event),
      id: order.id,
      orderItemCount: order.order_item_count,
      receiptNumber: order.receipt_number,
      submittedDate: order.submitted_date,
      total: order.total
    }
  end

  defp bill_data(%Bill{} = bill) do
    # styler:sort
    %{
      fee: bill.fee,
      id: bill.id,
      presaleFee: bill.presale_fee,
      presaleFeeTax: bill.presale_fee_tax,
      systemFee: bill.system_fee,
      systemFeeTax: bill.system_fee_tax,
      total: bill.total
    }
  end

  defp bill_data(_bill), do: nil

  defp event_json(%Event{} = event) do
    # styler:sort
    %{
      endDate: event.endDate,
      id: event.id,
      startDate: event.startDate,
      title: event.title,
      venue: venue_json(event.venue)
    }
  end

  defp event_json(_event), do: nil

  defp venue_json(%Venue{} = venue) do
    # styler:sort
    %{
      address: venue.address && address_json(venue.address),
      id: venue.id,
      name: venue.name
    }
  end

  defp venue_json(nil), do: nil

  defp address_json(%Address{} = address) do
    # styler:sort
    %{
      countryIso: address.countryIso,
      id: address.id,
      locality: address.locality,
      postalCode: address.postalCode,
      region: address.region,
      streetAddress: address.streetAddress
    }
  end

  defp address_json(_address), do: nil
end
