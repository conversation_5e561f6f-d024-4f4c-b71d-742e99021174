defmodule OrdersService.Seatsio do
  @moduledoc false

  alias ExServiceClient.Services.EventsService
  alias Seatsio.Objects, as: SeatsObjects

  require Logger

  @type seat_object() :: %{
          event_id: Ecto.UUID.t(),
          label: String.t()
        }

  @spec book_objects([seat_object()], Ecto.UUID.t() | nil, Ecto.UUID.t() | nil) ::
          {:ok, :no_objects_to_book | :all_objects_booked} | {:error, any()}
  def book_objects(tickets, hold_tokens, order_id \\ nil) do
    objects_with_label = filter_objects_with_label(tickets)

    if objects_with_label == [] do
      {:ok, :no_objects_to_book}
    else
      book_for_objects_with_hold_tokens(objects_with_label, hold_tokens, order_id)
    end
  end

  @spec get_object_info_for_order_items([map()]) :: {:ok, map()} | {:error, [any()]}
  def get_object_info_for_order_items(order_items) do
    order_items
    |> Enum.flat_map(fn
      %{"event" => %{id: event_id}, "seatConfigs" => seat_configs} ->
        for %{"label" => label} <- seat_configs, do: {event_id, label}

      _ ->
        []
    end)
    |> Enum.group_by(&elem(&1, 0), &elem(&1, 1))
    |> Enum.map(fn {event_key, labels} ->
      Task.async(fn -> get_objects_for_event_key(event_key, labels) end)
    end)
    |> Task.await_many()
    |> Enum.reduce({%{}, []}, fn
      {:ok, res}, {objects, errors} ->
        {Map.merge(objects, res), errors}

      {:error, error}, {objects, errors} ->
        {objects, [error | errors]}
    end)
    |> case do
      {objects, []} ->
        {:ok, objects}

      {_objects, errors} ->
        {:error, errors}
    end
  end

  @spec get_objects_for_event_key(Ecto.UUID.t(), [String.t()]) ::
          {:ok, map()} | {:error, :promoter_not_found | :seatsio_objects_error}
  def get_objects_for_event_key(_event_key, []), do: {:ok, %{}}

  def get_objects_for_event_key(event_key, labels) do
    with_promoter_workspace(event_key, fn workspace_key ->
      case SeatsObjects.get(workspace_key, event_key, labels) do
        {:ok, _} = res ->
          Logger.debug("Successfully fetched objects for event (#{inspect(event_key)})")
          res

        {:error, msg} ->
          Logger.error("Error fetching objects for event (#{inspect(event_key)}): #{inspect(msg)}")

          {:error, :seatsio_objects_error}
      end
    end)
  end

  @spec release_objects([seat_object()]) ::
          {:ok, :no_objects_to_release | :all_objects_released} | {:error, any()}
  def release_objects(objects) do
    objects
    |> filter_objects_with_label()
    |> case do
      [] ->
        {:ok, :no_objects_to_release}

      objects_with_labels ->
        release_for_objects(objects_with_labels)
    end
  end

  defp filter_objects_with_label(objects) do
    Enum.filter(objects, & &1.label)
  end

  defp with_promoter_workspace(event_key, func) do
    case EventsService.get_promoter_by_event_id(event_key) do
      {:ok, %{"data" => %{"privateWorkspaceKey" => workspace_key}}} when workspace_key != nil ->
        func.(workspace_key)

      {_type, msg} ->
        Logger.error("Error fetching promoter for event (#{inspect(event_key)}): #{inspect(msg)}")
        {:error, :promoter_not_found}
    end
  end

  # Processes grouped events using the provided function concurrently.
  #
  # This function takes a map of grouped events (where each key is an event_id and the value is a list of labels),
  # then applies the given function asynchronously to each group. It collects all results, returning {:ok, :success}
  # if all are successful, or {:error, errors} containing any encountered errors.
  #
  # ## Parameters
  #
  #   grouped_labels - A map where keys are event_ids and values are lists of labels.
  #   func - A function that takes (event_key, labels) and returns :ok or {:error, msg}.
  #
  # ## Returns
  #
  #   {:ok, :success} if all operations succeed.
  #   {:error, [errors]} containing any error messages from failed operations.
  @spec process_labels_grouped_by_event(%{Ecto.UUID.t() => [String.t()]}, ({Ecto.UUID.t(), [String.t()]} ->
                                                                             :ok | {:error, any()})) ::
          {:ok, :success} | {:error, list(any())}
  defp process_labels_grouped_by_event(grouped_labels, func) do
    grouped_labels
    |> Enum.map(fn label_group ->
      Task.async(fn -> func.(label_group) end)
    end)
    |> Task.await_many()
    |> Enum.reduce([], fn
      :ok, errors ->
        errors

      {:error, msg}, errors ->
        [msg | errors]
    end)
    |> case do
      [] ->
        {:ok, :success}

      errors ->
        {:error, errors}
    end
  end

  defp release_for_objects(objects) do
    objects
    |> group_object_labels_by_event()
    |> process_labels_grouped_by_event(&release_objects_for_event/1)
    |> case do
      {:ok, :success} ->
        {:ok, :all_objects_released}

      {:error, errors} ->
        Logger.error("Could not release all objects: #{inspect(errors)}")
        {:error, errors}
    end
  end

  defp release_objects_for_event({event_key, labels}) do
    with_promoter_workspace(event_key, fn workspace_key ->
      payload = %{objects: labels, ignoreChannels: true}

      case Seatsio.Events.release_objects(event_key, payload, workspace_key) do
        {:ok, _} ->
          Logger.info("Successfully released objects #{inspect(labels)} for event #{inspect(event_key)}")

          :ok

        {:error, msg} ->
          Logger.error("Error releasing objects for event (#{inspect(event_key)}): #{inspect(msg)}")

          {:error, :seatsio_release_error}
      end
    end)
  end

  defp book_for_objects_with_hold_tokens(objects, hold_tokens, order_id) do
    book_func = fn label_group ->
      book_objects_for_event(label_group, hold_tokens, order_id)
    end

    objects
    |> group_object_labels_by_event()
    |> process_labels_grouped_by_event(book_func)
    |> case do
      {:ok, :success} ->
        {:ok, :all_objects_booked}

      {:error, [:already_booked]} ->
        {:error, :already_booked}

      {:error, [:seatsio_book_error]} ->
        {:error, :seatsio_book_error}

      {:error, errors} ->
        Logger.critical("Could not book all Seats.io tickets: #{inspect(errors)}")
        {:error, errors}
    end
  end

  defp book_objects_for_event({event_key, labels}, hold_tokens, order_id) do
    with_promoter_workspace(event_key, fn workspace_key ->
      case SeatsObjects.book(workspace_key, event_key, labels, true, hold_tokens[event_key], order_id) do
        {:ok, _} ->
          Logger.info("Successfully booked objects (#{inspect(labels)}) for event (#{inspect(event_key)})")

          :ok

        {:error, msg} ->
          Logger.error("Error booking objects for event (#{inspect(event_key)}): #{inspect(msg)}")
          analyze_error_message(msg)
      end
    end)
  end

  defp analyze_error_message(%{message: %{"messages" => [message]}}) when is_binary(message) do
    if String.contains?(message, "already in that status") do
      {:error, :already_booked}
    else
      {:error, :seatsio_book_error}
    end
  end

  defp analyze_error_message(_msg) do
    {:error, :seatsio_book_error}
  end

  @spec group_object_labels_by_event([seat_object()]) :: %{Ecto.UUID.t() => [String.t()]}
  defp group_object_labels_by_event(objects) do
    Enum.group_by(objects, & &1.event_id, & &1.label)
  end
end
