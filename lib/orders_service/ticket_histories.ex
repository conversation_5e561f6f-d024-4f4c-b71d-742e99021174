defmodule OrdersService.TicketHistories do
  @moduledoc false

  import Ecto.Query

  alias OrdersService.Repo
  alias OrdersService.TicketHistory

  require Logger

  @spec count(map() | keyword()) :: integer() | nil
  def count(params) do
    TicketHistory
    |> where(^filter_where_params(params))
    |> select([th], count(th.id))
    |> Repo.one()
  end

  @spec build_scan_metadata(map()) :: map()
  def build_scan_metadata(params) do
    Map.new()
    |> maybe_add(:is_camera, params["is_camera"])
    |> maybe_add(:check_in_date, maybe_parse_date(params["checkInDate"]))
    |> maybe_add_sync_date()
  end

  defp filter_where_params(params) do
    Enum.reduce(params, dynamic(true), fn {key, value}, acc -> filter_where(acc, key, value) end)
  end

  defp filter_where(dynamic, :location_id, value), do: dynamic([th], ^dynamic and th.location_id == ^value)
  defp filter_where(dynamic, :location_type, value), do: dynamic([th], ^dynamic and th.location_type == ^value)

  defp filter_where(dynamic, _key, _value), do: dynamic

  defp maybe_add(map, _key, nil), do: map
  defp maybe_add(map, key, value), do: Map.put(map, key, value)

  defp maybe_parse_date(nil), do: nil

  defp maybe_parse_date(value) do
    case NaiveDateTime.from_iso8601(value) do
      {:ok, datetime} ->
        datetime
        |> DateTime.from_naive("Etc/UTC")
        |> elem(1)
        |> DateTime.truncate(:second)

      _ ->
        Logger.error("Failed to parse checkInDate with value #{inspect(value)}")
        nil
    end
  end

  defp maybe_add_sync_date(%{check_in_date: check_in_date} = map) when not is_nil(check_in_date) do
    Map.put(map, :sync_date, DateTime.truncate(DateTime.utc_now(), :second))
  end

  defp maybe_add_sync_date(map), do: map
end
