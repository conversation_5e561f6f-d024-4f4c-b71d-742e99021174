defmodule OrdersService.MixProject do
  use Mix.Project

  @version "4.31.0"

  def project do
    [
      app: :orders_service,
      version: @version,
      elixir: "~> 1.17",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      releases: [
        orders_service: [
          applications: [opentelemetry: :temporary]
        ]
      ]
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {OrdersService.Application, []},
      extra_applications: [:logger, :runtime_tools, :db_seeds]
    ]
  end

  # This makes sure your factory and any other modules in test/support are compiled
  # when in the test environment.
  defp elixirc_paths(:test), do: ["lib", "test/factories", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    # styler:sort
    pub_deps = [
      {:broadway, "~> 1.1"},
      {:broadway_cloud_pub_sub, "~> 0.9"},
      {:cors_plug, "~> 3.0"},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:csv, "~> 3.2"},
      {:db_seeds, "~> 0.0.1"},
      {:decorator, "~> 1.4"},
      {:dialyxir, "~> 1.4", only: [:dev, :test], runtime: false},
      {:ecto, "~> 3.11"},
      {:ecto_commons, "~> 0.3"},
      {:ecto_sql, "~> 3.11"},
      {:esbuild, "~> 0.7", runtime: Mix.env() == :dev},
      {:ex_machina, "~> 2.8.0", only: :test},
      {:ex_money, "~> 5.16"},
      {:ex_money_sql, "~> 1.11"},
      {:faker, "~> 0.18", only: :test},
      # override finch dependency in ex_firebase_auth
      {:finch, "~> 0.10", override: true},
      {:gettext, git: "https://github.com/jimpanse42/gettext", override: true},
      {:google_api_pub_sub, "== 0.38.0"},
      {:goth, "~> 1.4"},
      {:hackney, "~> 1.20"},
      {:hammer, "~> 6.2"},
      {:hammer_backend_redis, "~> 6.1"},
      {:jason, "~> 1.4", override: true},
      {:joken, "~> 2.5"},
      {:json, "~> 1.4"},
      {:logger_json, "~> 6.0"},
      {:nebulex, "~> 2.6"},
      {:nimble_csv, "~> 1.0"},
      {:oban, "2.18.3"},
      {:open_api_spex, "~> 3.18"},
      {:open_telemetry_decorator, "~> 1.5"},
      {:opentelemetry, "~> 1.3"},
      {:opentelemetry_api, "~> 1.2"},
      {:opentelemetry_cowboy, "~> 0.2"},
      {:opentelemetry_ecto, "~> 1.2"},
      {:opentelemetry_exporter, "~> 1.6"},
      {:opentelemetry_phoenix, "~> 1.1"},
      {:opentelemetry_tesla, "~> 2.2.0"},
      {:params, "~> 2.2"},
      {:phoenix, "~> 1.7"},
      {:phoenix_ecto, "~> 4.6"},
      {:plug_cowboy, "~> 2.5"},
      {:poison, "~> 6.0", override: true},
      {:postgrex, ">= 0.0.0"},
      {:proper_case, "~> 1.3"},
      {:recase, "~> 0.8", override: true},
      {:scrivener_ecto, "~> 2.0"},
      {:seqfuzz, "~> 0.2.0"},
      {:shortuuid, "~> 3.0"},
      {:ssl_verify_fun, "~> 1.1.7", manager: :rebar3, override: true},
      # there is a bug in 1.4.2
      {:styler, "1.4.0", only: [:dev, :test], runtime: false},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:tesla, "~> 1.11"},
      {:timex, "~> 3.7"},
      {:tzdata, "~> 1.1.2", override: true},
      # fix unleash to resolve dependencies conflict with telemetry
      {:unleash, "1.9.0", optional: true},
      {:versioce, "~> 2.0", override: true},
      {:ymlr, "~> 5.1.3"}
    ]

    priv_deps =
      if Mix.env() == :dev do
        [
          {:adyen, path: "../ex_adyen_elixir_api_library", override: true},
          {:ex_firebase_auth_plug, path: "../ex_firebase_auth_plug", override: true},
          {:ex_ikarus, path: "../ex_ikarus", override: true},
          {:ex_rbac, path: "../ex_rbac", override: true},
          {:ex_service_client, path: "../ex_service_client", override: true},
          {:seatsio, path: "../ex_seatsio"},
          {:secrets, path: "../ex_secrets", override: true}
        ]
      else
        [
          {:adyen, "~> 0.1.20", organization: "stagedates", override: true},
          {:ex_firebase_auth_plug, "~> 0.2.13", organization: "stagedates", override: true},
          {:ex_ikarus, "~> 2.0", organization: "stagedates", override: true},
          {:ex_rbac, "~> 1.5", organization: "stagedates", override: true},
          {:ex_service_client, "~> 1.11", organization: "stagedates", override: true},
          {:seatsio, "~> 0.3.3", organization: "stagedates", override: true},
          {:secrets, "~> 0.1.0", organization: "stagedates", override: true}
        ]
      end

    pub_deps ++ priv_deps
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.drop", "ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["esbuild.install --if-missing"],
      "assets.build": ["esbuild default"],
      "assets.deploy": ["esbuild default --minify", "phx.digest"],
      "test:ci": ["ecto.drop", "ecto.create --quiet", "ecto.migrate --quiet", "test"]
    ]
  end
end
