@startuml seller
  ' uncomment the line below if you're using computer with a retina display
  ' skinparam dpi 300
  !define Table(name,desc) class name as "desc" << (T,#FFAAAA) >>
  ' we use bold for primary key
  ' green color for unique
  ' and underscore for not_null
  !define primary_key(x) <b>x</b>
  !define unique(x) <color:green>x</color>
  !define not_null(x) <u>x</u>
  !define future_work(x) <color: grey>x</color>
  ' other tags available:
  ' <i></i>
  ' <back:COLOR></color>, where color is a color name or html color code
  ' (#FFAACC)
  ' see: http://plantuml.com/classes.html#More
  hide methods
  hide stereotypes

  legend right
    Legend:
    | primary_key(Primary Key) |
    | unique(Unique) |
    | not_null(Not Null) |
    | future_work(Future work) |
  end legend

  title
    Seller Model
  end title

  package "OrdersService" as OrderServicePkg <<Frame>> #CCCCCC {
    entity Order{}
  }

  package "EventsService" as EventsServicePkg <<Frame>> #CCCCCC {
    entity Event { }

    entity SalesChannel {}

    enum SalesPermissionType {
      ORGANIZER
      EVENT
      SALES_CHANNEL
    }

    enum SellerType {
      ORGANIZER
      PLATFORM
      SHOP
      STORE
      POS
      RESELLER
    }

    enum SellerPermissionRole {
        ADMIN
        OWNER
    }

    ' Seller: Somebody who sells tickets for an event or sales channel. The seller is modeled as a polymorphic entity with child entities for the different types of sellers.
    entity Seller {
      primary_key(id): uuid <<generated>>
      parent_id: uuid <<FK>>
      not_null(owner_id): uuid <<FK to User>>
      not_null(type): SellerType
      not_null(type_id): uuid <<Polymorphic FK>>
      deleted_at: utc_datetime
    }

    ' The organizer of an event. Can be a person or a company.
    ' Typical organizers will sell their ticket via the stagedates platform, their website and maybe a local ticket outlet. Those can me added as child entities.
    entity Organizer {
        primary_key(id): uuid <<generated>>
        not_null(promoter_id): uuid <<FK to Promoter>>
        display_name: string
        deleted_at: utc_datetime
    }

    entity Promoter {
        primary_key(id): uuid <<generated>>
        display_name: string
        created_by_document_id: string
    }

    ' Store = BookingOffice / Local Ticket Outlet Organization
    ' A physical location people can go to buy tickets. Can also be used to model box offices at venues.
    entity Store {
      primary_key(id): uuid <<generated>>
      not_null(address_id): uuid <<FK to Address>>
      name: string
      deleted_at: utc_datetime
    }
    
    entity Address {}

    ' Point of Sale: usually used as child entity of Store
    entity POS {
      pin: number
    }

    ' Shop = Online Ticket Shop. The virtual pendant to a Store
    entity Shop {
      url: varchar
    }

    ' Platform: The entity that operates a ticketing platform. Can sell ticket contingents at their own conditions. Examples are stagedates itself and TicketSwap. 
    entity Platform {
      api_key: varchar
    }

    ' Reseller: Somebody who sells tickets on behalf of the organizer and gets a commision for each ticket sold. For example influcencers, bloggers, etc.
    entity Reseller {}

    ' SalesPermission: Assigns the permission to sell tickets for an event, sales channel, or for an entire organizer to a seller. 
    ' All children of Seller inherit the permissions from the parent
    entity SalesPermission {
      primary_key(id): uuid <<generated>>
      not_null(type): SalesPermissionType
      not_null(seller_id): uuid <<FK to Seller>>
      not_null(type_id): uuid <<Polymorphic FK>>
      deleted_at: utc_datetime
    }

    ' SellerPermission: Defines user roles (e.g., ADMIN, OWNER) for a specific seller account.
    entity SellerPermission {
      primary_key(id): uuid <<generated>>
      not_null(seller_id): uuid <<FK to Seller>>
      user_id: uuid <<FK to User>>
      user_document_id: string
      not_null(roles): SellerPermissionRole[]
    }

    Seller <|-- Organizer
    Seller <|-- Store
    Seller <|-- POS
    Seller <|-- Shop
    Seller <|-- Platform
    Seller <|-- Reseller

    Promoter "1" -- "1" Organizer
    Store "1" -- "1" Address

    Seller "1" -- "n" SellerPermission
    Seller "1" -- "n" SalesPermission
    Seller "0..1" -- "n" Seller: parent >

    SalesPermission -- SalesPermissionType
    Seller -- SellerType
    SellerPermission -- SellerPermissionRole

  }

  package "AccountsService" as AccountsServicePkg <<Frame>> #CCCCCC {
    entity User {}
    entity Role {
      not_null(name): varchar 
    }

    User "n" <--> "m" Role
  }

  ' Relationships
  SalesChannel "1" -- "n" SalesPermission
  Event "1" -- "n" SalesPermission
  EventsServicePkg.Promoter "1" -- "n" SalesPermission

  EventsServicePkg.Seller "1" -- "n" OrderServicePkg.Order 
  EventsServicePkg.Seller "1" -- "1" AccountsServicePkg.User: owner
  AccountsServicePkg.User "1" -- "n" EventsServicePkg.SellerPermission

@enduml